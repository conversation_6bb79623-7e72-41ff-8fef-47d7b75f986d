import jwt
import logging
from typing import Optional
from open_webui.models.users import Users
from open_webui.models.auths import Auths
from open_webui.env import WEBUI_SECRET_KEY
from open_webui.utils.auth import create_token, create_api_key

log = logging.getLogger(__name__)

# Bot configuration
BOT_USER_TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjYxZTZlZjhhLWNhMDItNDI5OS05ZTcyLTVjM2VhMDFjNTA2NiJ9.or-iENCS0mPZ1aXKotdx3r7AWMAUSAzAaDazf-61jWo"
BOT_USER_ID = None  # Will be extracted from token

def decode_bot_token() -> Optional[str]:
    """Decode the bot JWT token to extract user ID"""
    try:
        # First try with current secret key
        decoded = jwt.decode(BOT_USER_TOKEN, WEBUI_SECRET_KEY, algorithms=["HS256"])
        user_id = decoded.get("id")
        log.info(f"🤖 Bot token decoded successfully. User ID: {user_id}")
        return user_id
    except jwt.InvalidSignatureError:
        # If signature fails, try to decode without verification to see contents
        try:
            decoded = jwt.decode(BOT_USER_TOKEN, options={"verify_signature": False})
            user_id = decoded.get("id")
            log.warning(f"⚠️ Bot token signature invalid, but decoded without verification. User ID: {user_id}")
            log.warning(f"⚠️ Current WEBUI_SECRET_KEY: {WEBUI_SECRET_KEY[:20]}...")
            return user_id
        except Exception as e:
            log.error(f"❌ Failed to decode token even without verification: {e}")
            return None
    except jwt.InvalidTokenError as e:
        log.error(f"❌ Failed to decode bot token: {e}")
        return None
    except Exception as e:
        log.error(f"❌ Unexpected error decoding bot token: {e}")
        return None

def verify_bot_user() -> bool:
    """Verify that the bot user exists in the database"""
    global BOT_USER_ID

    # Extract user ID from token
    BOT_USER_ID = decode_bot_token()
    if not BOT_USER_ID:
        log.error("❌ Could not extract bot user ID from token")
        return False

    # Check if user exists in database
    try:
        bot_user = Users.get_user_by_id(BOT_USER_ID)
        if bot_user:
            log.info(f"✅ Bot user found: {bot_user.name} ({bot_user.email}) - Role: {bot_user.role}")
            return True
        else:
            log.error(f"❌ Bot user with ID {BOT_USER_ID} not found in database")
            return False
    except Exception as e:
        log.error(f"❌ Error checking bot user: {e}")
        return False

def get_bot_user_id() -> Optional[str]:
    """Get the bot user ID (initialize if needed)"""
    global BOT_USER_ID
    if BOT_USER_ID is None:
        verify_bot_user()
    return BOT_USER_ID

def create_bot_user() -> bool:
    """Create the bot user in the database"""
    global BOT_USER_ID, BOT_USER_TOKEN

    try:
        # Create bot user with authentication
        bot_user = Auths.insert_new_auth(
            email="<EMAIL>",
            password="bot-password-not-used",  # Bot uses token auth, not password
            name="OpenWebUI Bot",
            profile_image_url="/user.png",
            role="user"  # Regular user role
        )

        if bot_user:
            # Update global variables with the new user
            BOT_USER_ID = bot_user.id

            # Generate new token for the bot user
            BOT_USER_TOKEN = create_token(data={"id": bot_user.id})

            log.info(f"✅ Bot user created successfully: {bot_user.name} ({bot_user.email})")
            log.info(f"✅ Bot user ID: {bot_user.id}")
            log.info(f"✅ New bot token: {BOT_USER_TOKEN}")

            # Generate API key for the bot
            api_key = create_api_key()
            Users.update_user_api_key_by_id(bot_user.id, api_key)
            log.info(f"✅ Bot API key generated: {api_key}")

            return True
        else:
            log.error(f"❌ Failed to create bot user")
            return False

    except Exception as e:
        log.error(f"❌ Error creating bot user: {e}")
        import traceback
        traceback.print_exc()
        return False

def initialize_bot() -> bool:
    """Initialize the bot service"""
    log.info("🚀 Initializing bot service...")

    if verify_bot_user():
        log.info("✅ Bot service initialized successfully")
        return True
    else:
        log.warning("⚠️ Bot user not found, attempting to create...")
        if create_bot_user():
            log.info("✅ Bot service initialized successfully after creating user")
            return True
        else:
            log.error("❌ Bot service initialization failed")
            return False

# Test function to verify everything works
def test_bot_setup():
    """Test function to verify bot setup"""
    print("🧪 Testing bot setup...")
    print(f"📋 Bot token: {BOT_USER_TOKEN[:50]}...")

    # Test token decoding
    user_id = decode_bot_token()
    if user_id:
        print(f"✅ Token decoded successfully: {user_id}")
    else:
        print("❌ Token decoding failed")
        return False

    # Check what users exist in the database
    try:
        users_response = Users.get_users()
        all_users = users_response["users"]
        print(f"📊 Found {len(all_users)} users in database:")
        for user in all_users[:5]:  # Show first 5 users
            print(f"   - {user.name} ({user.email}) - ID: {user.id} - Role: {user.role}")
        if len(all_users) > 5:
            print(f"   ... and {len(all_users) - 5} more users")
    except Exception as e:
        print(f"❌ Error getting users: {e}")
        import traceback
        traceback.print_exc()

    # Test bot initialization (which includes user creation if needed)
    if initialize_bot():
        print("✅ Bot initialization successful")
        print(f"🤖 Bot user ID: {get_bot_user_id()}")
        print(f"🔑 Bot token: {BOT_USER_TOKEN}")
        return True
    else:
        print("❌ Bot initialization failed")
        return False

if __name__ == "__main__":
    # Run test when script is executed directly
    test_bot_setup()
