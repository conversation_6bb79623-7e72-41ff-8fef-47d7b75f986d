<script>
	import { getContext, onMount } from 'svelte';
	import { toast } from 'svelte-sonner';
	import { getAllUsers, deleteUserById } from '$lib/apis/users';
	import { getGroups, createNewGroup, updateGroupById, deleteGroupById } from '$lib/apis/groups';
	import { addUser } from '$lib/apis/auths';
	import { generateInitialsImage } from '$lib/utils';
	import { getChannels } from '$lib/apis/channels';
	import { channels, socket, user } from '$lib/stores';

	import UsersSolid from '$lib/components/icons/UsersSolid.svelte';
	import UserPlusSolid from '$lib/components/icons/UserPlusSolid.svelte';
	import Pencil from '$lib/components/icons/Pencil.svelte';
	import Search from '$lib/components/icons/Search.svelte';
	import ShieldCheck from '$lib/components/icons/ShieldCheck.svelte';
	import GarbageBin from '$lib/components/icons/GarbageBin.svelte';

	import AddGroupModal from './Groups/AddGroupModal.svelte';
	import EditGroupModal from './Groups/EditGroupModal.svelte';
	import AddUserModal from './UserList/AddUserModal.svelte';
	import EditUserModal from './UserList/EditUserModal.svelte';
	import ConfirmDialog from '$lib/components/common/ConfirmDialog.svelte';
	import UserPermissionsModal from './UserPermissionsModal.svelte';

	const i18n = getContext('i18n');

	let loaded = false;
	let users = [];
	let groups = [];
	let filteredUsers = [];
	let searchQuery = '';
	let selectedTeamFilter = 'All Teams';
	let selectedRoleFilter = 'All Roles';

	// Pagination for users
	let usersPage = 1;
	let usersPerPage = 6; // Match the image showing 6 users per page
	let paginatedUsers = [];

	// Modal states
	let showCreateTeamModal = false;
	let showEditTeamModal = false;
	let showInviteUserModal = false;
	let showEditUserModal = false;
	let showDeleteConfirmDialog = false;
	let showUserPermissionsModal = false;
	let selectedGroup = null;
	let selectedUser = null;

	// Filter options
	$: teamOptions = ['All Teams', ...groups.map((g) => g.name)];
	$: roleOptions = ['All Roles', 'admin', 'user'];

	// Filter users based on search and filters
	$: {
		filteredUsers = users.filter((user) => {
			const matchesSearch =
				searchQuery === '' ||
				user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				user.email.toLowerCase().includes(searchQuery.toLowerCase());

			const matchesTeam =
				selectedTeamFilter === 'All Teams' ||
				groups.some(
					(group) =>
						group.name === selectedTeamFilter && group.user_ids && group.user_ids.includes(user.id)
				);

			const matchesRole = selectedRoleFilter === 'All Roles' || user.role === selectedRoleFilter;

			return matchesSearch && matchesTeam && matchesRole;
		});
	}

	// Paginate users
	$: {
		const startIndex = (usersPage - 1) * usersPerPage;
		const endIndex = startIndex + usersPerPage;
		paginatedUsers = filteredUsers.slice(startIndex, endIndex);
	}

	// Reset pagination when filters change
	$: if (searchQuery || selectedTeamFilter || selectedRoleFilter) {
		usersPage = 1;
	}

	const loadData = async () => {
		try {
			const [usersRes, groupsRes] = await Promise.all([
				getAllUsers(localStorage.token),
				getGroups(localStorage.token)
			]);

			if (usersRes) {
				users = usersRes.users || [];
			}
			if (groupsRes) {
				groups = groupsRes || [];
			}
		} catch (error) {
			console.error('Error loading data:', error);
			toast.error(`Error loading data: ${error}`);
		}
	};

	const refreshChannels = async () => {
		try {
			// Refresh channels to show new team channels
			const channelList = await getChannels(localStorage.token);
			if (channelList) {
				channels.set(channelList);
			}

			// Also emit socket event to join new channels
			if ($socket && localStorage.token) {
				$socket.emit('join-channels', { auth: { token: localStorage.token } });
			}
		} catch (error) {
			console.error('Error refreshing channels:', error);
		}
	};

	const getUserTeam = (userId) => {
		const userGroup = groups.find((group) => group.user_ids && group.user_ids.includes(userId));
		return userGroup ? userGroup.name : 'No Team';
	};

	const getRoleBadgeClass = (role) => {
		switch (role) {
			case 'admin':
				return 'bg-purple-600 text-white';
			case 'user':
				return 'bg-green-600 text-white';
			case 'superadmin':
				return 'bg-red-600 text-white';
			default:
				return 'bg-gray-600 text-white';
		}
	};

	const formatDate = (timestamp) => {
		return new Date(timestamp * 1000).toLocaleDateString();
	};

	const getAvatarColor = (name) => {
		const colors = [
			'bg-blue-500',
			'bg-green-500',
			'bg-purple-500',
			'bg-pink-500',
			'bg-indigo-500',
			'bg-yellow-500',
			'bg-red-500',
			'bg-teal-500'
		];
		const index = name.charCodeAt(0) % colors.length;
		return colors[index];
	};

	const deleteUserHandler = async (userId) => {
		try {
			const res = await deleteUserById(localStorage.token, userId);
			if (res) {
				toast.success('User deleted successfully');
				await loadData(); // Reload the data to refresh the table
			}
		} catch (error) {
			console.error('Error deleting user:', error);
			toast.error(`Failed to delete user: ${error}`);
		}
	};

	const handleUserPermissions = (user) => {
		selectedUser = user;
		showUserPermissionsModal = true;
	};

	const handleCreateTeam = async (teamData) => {
		try {
			// First create the team/group
			const group = await createNewGroup(localStorage.token, {
				name: teamData.name,
				description: `Team with ${teamData.adminCount} admins and ${teamData.userCount} users`
			});

			if (!group) {
				throw new Error('Failed to create team');
			}

			const createdUserIds = [];

			// Create admin users
			for (let i = 1; i <= teamData.adminCount; i++) {
				const adminEmail = `${teamData.name.toLowerCase().replace(/\s+/g, '')}-admin${i}@example.com`;
				const adminName = `${teamData.name} Admin ${i}`;
				const adminPassword = `admin${i}123!`;

				try {
					const adminUser = await addUser(
						localStorage.token,
						adminName,
						adminEmail,
						adminPassword,
						'admin',
						generateInitialsImage(adminName)
					);

					if (adminUser) {
						createdUserIds.push(adminUser.id);
					}
				} catch (error) {
					console.error(`Failed to create admin ${i}:`, error);
					toast.error(`Failed to create admin ${i}: ${error}`);
				}
			}

			// Create regular users
			for (let i = 1; i <= teamData.userCount; i++) {
				const userEmail = `${teamData.name.toLowerCase().replace(/\s+/g, '')}-user${i}@example.com`;
				const userName = `${teamData.name} User ${i}`;
				const userPassword = `user${i}123!`;

				try {
					const regularUser = await addUser(
						localStorage.token,
						userName,
						userEmail,
						userPassword,
						'user',
						generateInitialsImage(userName)
					);

					if (regularUser) {
						createdUserIds.push(regularUser.id);
					}
				} catch (error) {
					console.error(`Failed to create user ${i}:`, error);
					toast.error(`Failed to create user ${i}: ${error}`);
				}
			}

			// Update the group with the created user IDs
			if (createdUserIds.length > 0) {
				try {
					const updatedGroup = await updateGroupById(localStorage.token, group.id, {
						name: group.name,
						description: group.description,
						user_ids: createdUserIds
					});

					if (updatedGroup) {
						toast.success(
							`Team "${teamData.name}" created successfully with ${createdUserIds.length} members. Team channel is now available!`
						);
						// Refresh channels to show the new team channel
						await refreshChannels();
					} else {
						toast.warning(`Team "${teamData.name}" created but failed to assign users to team`);
					}
				} catch (error) {
					console.error('Error updating group with user IDs:', error);
					toast.warning(`Team "${teamData.name}" created but failed to assign users: ${error}`);
				}
			} else {
				toast.warning(`Team "${teamData.name}" created but no users were added`);
			}
		} catch (error) {
			console.error('Error creating team:', error);
			toast.error(`Failed to create team: ${error}`);
		}
	};

	const handleUpdateTeam = async (teamData, groupId) => {
		try {
			// Update the team/group basic info
			const updatedGroup = await updateGroupById(localStorage.token, groupId, {
				name: teamData.name,
				description: `Team with ${teamData.adminCount} admins and ${teamData.userCount} users`
			});

			if (!updatedGroup) {
				throw new Error('Failed to update team');
			}

			// For now, just update the basic team info
			// In a full implementation, you might want to handle user count changes
			// by creating/removing users as needed

			toast.success(`Team "${teamData.name}" updated successfully`);
		} catch (error) {
			console.error('Error updating team:', error);
			toast.error(`Failed to update team: ${error}`);
		}
	};

	const handleDeleteTeam = async (groupId) => {
		try {
			const result = await deleteGroupById(localStorage.token, groupId);
			if (result) {
				toast.success('Team deleted successfully');
			} else {
				throw new Error('Failed to delete team');
			}
		} catch (error) {
			console.error('Error deleting team:', error);
			toast.error(`Failed to delete team: ${error}`);
		}
	};

	onMount(async () => {
		await loadData();
		loaded = true;
	});
</script>

{#if loaded}
	<!-- Header with action buttons -->
	<div class="flex justify-between items-center mb-6">
		<h1 class="text-2xl font-semibold text-white">Team Management</h1>
		<div class="flex gap-3">
			<button
				class="flex items-center gap-2 px-4 py-2.5 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
				on:click={() => (showInviteUserModal = true)}
			>
				<UserPlusSolid className="size-4" />
				Invite User
			</button>
			<button
				class="flex items-center gap-2 px-4 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
				on:click={() => (showCreateTeamModal = true)}
			>
				<UsersSolid className="size-4" />
				Create Team
			</button>
		</div>
	</div>

	<!-- Teams Overview Section -->
	<div class="mb-8">
		<h2 class="text-lg font-medium text-white mb-4">Teams Overview</h2>
		{#if groups && groups.length > 0}
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
				{#each groups as group}
					<div
						class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4"
					>
						<div class="flex items-start justify-between mb-3">
							<div class="flex items-center gap-2">
								<UsersSolid className="size-5 text-gray-600 dark:text-gray-400" />
								<h3 class="font-medium text-gray-900 dark:text-white">{group.name}</h3>
							</div>
							<span class="text-sm text-green-600 dark:text-green-400 font-medium">Active</span>
						</div>

						<div class="space-y-3 mb-4">
							<div class="flex justify-between items-center">
								<span class="text-sm text-gray-600 dark:text-gray-400">Admins</span>
								<span class="text-sm font-medium text-gray-900 dark:text-white">
									{users.filter(
										(u) => group.user_ids && group.user_ids.includes(u.id) && u.role === 'admin'
									).length}/{users.filter((u) => group.user_ids && group.user_ids.includes(u.id))
										.length}
								</span>
							</div>
							<div class="flex justify-between items-center">
								<span class="text-sm text-gray-600 dark:text-gray-400">Users</span>
								<span class="text-sm font-medium text-gray-900 dark:text-white">
									{group.user_ids ? group.user_ids.length : 0}/{group.user_ids
										? group.user_ids.length
										: 0}
								</span>
							</div>
						</div>

						<button
							class="w-full px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-md transition-colors"
							on:click={() => {
								selectedGroup = group;
								showEditTeamModal = true;
							}}
						>
							Manage Team
						</button>
					</div>
				{/each}
			</div>
		{:else}
			<div
				class="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-8 text-center"
			>
				<UsersSolid className="size-12 text-gray-400 mx-auto mb-4" />
				<h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No teams yet</h3>
				<p class="text-gray-600 dark:text-gray-400 mb-4">
					Create your first team to organize users and manage permissions.
				</p>
				<button
					class="flex items-center gap-2 px-4 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium"
					on:click={() => (showCreateTeamModal = true)}
				>
					<UsersSolid className="size-4" />
					Create Your First Team
				</button>
			</div>
		{/if}
	</div>

	<!-- Search and Filters -->
	<div class="flex flex-col sm:flex-row gap-4 mb-6">
		<div class="flex-1 relative">
			<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-gray-400" />
			<input
				type="text"
				placeholder="Search by name, email, or role"
				class="w-full pl-10 pr-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
				bind:value={searchQuery}
			/>
		</div>

		<div class="flex gap-3">
			<select
				class="px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]"
				bind:value={selectedTeamFilter}
			>
				{#each teamOptions as option}
					<option value={option}>{option}</option>
				{/each}
			</select>

			<select
				class="px-4 py-3 bg-gray-800 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[120px]"
				bind:value={selectedRoleFilter}
			>
				{#each roleOptions as option}
					<option value={option}>{option}</option>
				{/each}
			</select>
		</div>
	</div>

	<!-- Users Table -->
	<div class="bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
		<div class="overflow-x-auto">
			<table class="w-full">
				<thead class="bg-gray-800">
					<tr>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Name</th
						>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Email</th
						>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Role</th
						>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Team</th
						>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Created On</th
						>
						<th
							class="px-6 py-4 text-left text-xs font-medium text-gray-300 uppercase tracking-wider"
							>Actions</th
						>
					</tr>
				</thead>
				<tbody class="bg-gray-900 divide-y divide-gray-700">
					{#each paginatedUsers as user}
						<tr class="hover:bg-gray-700">
							<td class="px-6 py-4 whitespace-nowrap">
								<div class="flex items-center">
									<div class="flex-shrink-0 h-10 w-10">
										<div
											class="h-10 w-10 rounded-full {getAvatarColor(
												user.name
											)} flex items-center justify-center text-white text-sm font-medium"
										>
											{user.name.charAt(0).toUpperCase()}
										</div>
									</div>
									<div class="ml-3">
										<div class="text-sm font-medium text-white">{user.name}</div>
									</div>
								</div>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{user.email}</td>
							<td class="px-6 py-4 whitespace-nowrap">
								<span
									class="inline-flex px-3 py-1 text-xs font-medium rounded-full {getRoleBadgeClass(
										user.role
									)}"
								>
									{user.role.charAt(0).toUpperCase() + user.role.slice(1)}
								</span>
							</td>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300"
								>{getUserTeam(user.id)}</td
							>
							<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300"
								>{formatDate(user.created_at)}</td
							>
							<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
								<div class="flex items-center gap-2">
									<!-- Permission Icon (only for admin users) -->
									{#if user.role === 'admin'}
										<button
											class="text-blue-400 hover:text-blue-300 transition-colors"
											on:click={() => handleUserPermissions(user)}
											title="User Permissions"
										>
											<ShieldCheck className="size-4" />
										</button>
									{/if}

									<!-- Edit Icon -->
									<button
										class="text-blue-400 hover:text-blue-300 transition-colors"
										on:click={() => {
											selectedUser = user;
											showEditUserModal = true;
										}}
										title="Edit User"
									>
										<Pencil className="size-4" />
									</button>

									<!-- Delete Icon -->
									<button
										class="text-red-400 hover:text-red-300 transition-colors"
										on:click={() => {
											selectedUser = user;
											showDeleteConfirmDialog = true;
										}}
										title="Delete User"
									>
										<GarbageBin className="size-4" />
									</button>
								</div>
							</td>
						</tr>
					{/each}
				</tbody>
			</table>
		</div>
	</div>

	<!-- Pagination -->
	<div class="mt-4 px-4 py-3">
		<div class="flex items-center justify-between">
			<div class="text-sm text-gray-400">
				Showing <span class="font-medium text-white">{(usersPage - 1) * usersPerPage + 1}</span
				>-<span class="font-medium text-white"
					>{Math.min(usersPage * usersPerPage, filteredUsers.length)}</span
				>
				of <span class="font-medium text-white">{filteredUsers.length}</span> team members
			</div>
			{#if filteredUsers.length > usersPerPage}
				<div class="flex items-center space-x-2">
					<!-- Previous button -->
					<button
						class="px-3 py-1 text-sm text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
						disabled={usersPage === 1}
						on:click={() => (usersPage = Math.max(1, usersPage - 1))}
					>
						<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M15 19l-7-7 7-7"
							></path>
						</svg>
					</button>

					<!-- Page numbers -->
					{#each Array.from({ length: Math.ceil(filteredUsers.length / usersPerPage) }, (_, i) => i + 1) as pageNum}
						{#if pageNum === usersPage}
							<button
								class="px-3 py-1 text-sm bg-blue-600 text-white rounded"
								on:click={() => (usersPage = pageNum)}
							>
								{pageNum}
							</button>
						{:else}
							<button
								class="px-3 py-1 text-sm text-gray-400 hover:text-white hover:bg-gray-700 rounded"
								on:click={() => (usersPage = pageNum)}
							>
								{pageNum}
							</button>
						{/if}
					{/each}

					<!-- Next button -->
					<button
						class="px-3 py-1 text-sm text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
						disabled={usersPage === Math.ceil(filteredUsers.length / usersPerPage)}
						on:click={() =>
							(usersPage = Math.min(Math.ceil(filteredUsers.length / usersPerPage), usersPage + 1))}
					>
						<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"
							></path>
						</svg>
					</button>
				</div>
			{/if}
		</div>
	</div>

	<!-- Modals -->
	{#if showCreateTeamModal}
		<AddGroupModal
			bind:show={showCreateTeamModal}
			onSubmit={async (teamData) => {
				await handleCreateTeam(teamData);
				await loadData();
				showCreateTeamModal = false;
			}}
		/>
	{/if}

	{#if showEditTeamModal && selectedGroup}
		<EditGroupModal
			bind:show={showEditTeamModal}
			group={selectedGroup}
			{users}
			edit={true}
			onSubmit={async (teamData) => {
				await handleUpdateTeam(teamData, selectedGroup.id);
				await loadData();
				showEditTeamModal = false;
			}}
			onDelete={async () => {
				await handleDeleteTeam(selectedGroup.id);
				await loadData();
				showEditTeamModal = false;
			}}
		/>
	{/if}

	{#if showInviteUserModal}
		<AddUserModal
			bind:show={showInviteUserModal}
			on:save={async () => {
				await loadData();
				showInviteUserModal = false;
			}}
		/>
	{/if}

	{#if showEditUserModal && selectedUser}
		<EditUserModal
			bind:show={showEditUserModal}
			{selectedUser}
			sessionUser={selectedUser}
			on:save={async () => {
				await loadData();
				showEditUserModal = false;
			}}
		/>
	{/if}

	<!-- Delete Confirmation Dialog -->
	<ConfirmDialog
		bind:show={showDeleteConfirmDialog}
		title="Delete User"
		message="Are you sure you want to delete this user? This action cannot be undone."
		on:confirm={() => {
			if (selectedUser) {
				deleteUserHandler(selectedUser.id);
			}
		}}
	/>

	<!-- User Permissions Modal -->
	{#if selectedUser}
		<UserPermissionsModal
			bind:show={showUserPermissionsModal}
			user={selectedUser}
			on:save={async () => {
				await loadData();
				showUserPermissionsModal = false;
			}}
		/>
	{/if}
{/if}
